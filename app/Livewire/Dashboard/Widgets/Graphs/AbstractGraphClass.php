<?php

namespace App\Livewire\Dashboard\Widgets\Graphs;

use App\Enums\DiagramType;
use App\Services\Dashboard\WidgetViewService;
use App\Traits\LivewireGeneralFunctions;
use App\Traits\PoolAnalysesGeneralFunctions;
use Livewire\Attributes\On;
use Livewire\Component;

abstract class AbstractGraphClass extends Component
{
    use LivewireGeneralFunctions, PoolAnalysesGeneralFunctions;

    public $uniqueId;
    public $poolId;
    public $settings;
    public $results;
    public $labels;
    public $fullResults;
    public $filter;
    public $selectedLongDay;
    protected static DiagramType $diagramType;

    public function mount($poolId, $settings = [])
    {
        $this->uniqueId = $this->poolId . '_' . rand();
        $this->selectedLongDay = request()->query('longDay');
        $this->poolId = (int)$poolId;
        $this->settings = $settings;
        $this->fetchResults();
    }

    #[On('daySelected')]
    public function handleDaySelected($day)
    {
        $this->selectedLongDay = $day;
        $this->fetchResults();
    }

    protected function fetchResults()
    {
        $widgetService = app(WidgetViewService::class);
        $sorting = $this->settings['sorting'] ?? [];

        $results = $widgetService->processWidgetView(
            $this->poolId,
            static::$diagramType->value,
            ['longDay' => $this->selectedLongDay],
            $sorting
        ) ?? [];

        $this->fullResults = $results['full_results'] ?? [];
        $this->results = $results['results'] ?? [];
        $this->labels = $results['labels'] ?? [];
        $this->filter = $results['filter'] ?? false;
    }
}