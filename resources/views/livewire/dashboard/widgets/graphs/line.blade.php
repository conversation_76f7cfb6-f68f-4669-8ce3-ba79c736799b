<div class="line-chart-container">
    @php
    $barThickness = 15;
    @endphp

    <div id="{{$uniqueId}}_horizontal_line_container" class="horizontal-line-container"
        style="display: none; min-height: {{ count($fullResults) > 7 ? '180px' : '160px' }};">
        <div class="horizontal-legend">
            @foreach($fullResults as $item)
            <div class="horizontal-legend-item" style="height: {{ $barThickness }}px;">
                <div class="legend-label" title="{{ $item['name'] }}">{{ $item['name'] }}</div>
                <div class="legend-buttons">
                    @if($allowInfo ?? true)
                    <span class="btn-legend btn-info" data-poolid="{{ $poolId ?? '' }}"
                        data-analysisid="{{ $item['analysis_id'] }}" data-container="body" data-toggle="popover"
                        data-placement="bottom" data-html="true" data-content="">
                        <i class="fas fa-info-circle"></i>
                    </span>
                    @endif
                    <div class="add-to-cart-container">
                        <livewire:dashboard.single-analyses-add-to-cart
                            :wire:key="'chart-add-to-cart-line-h-' . $item['analysis_id']" :poolId="$poolId"
                            :analysesId="$item['analysis_id']" />
                    </div>
                </div>
            </div>
            @endforeach
        </div>
        <div class="horizontal-chart-wrapper">
            <canvas id="{{$uniqueId}}_module_head_chart_horizontal" class="horizontalBar"
                style="max-height: 180px;"></canvas>
        </div>
    </div>

    <div id="{{$uniqueId}}_regular_line_container" class="regular-line-container">
        <div class="line-chart-wrapper">
            <canvas id="{{$uniqueId}}_module_head_chart" style="max-height: 400px; min-height: 300px;"></canvas>
        </div>
        <div class="line-chart-legend">
            @foreach($fullResults as $index => $item)
            <div class="line-legend-item">
                <div class="legend-color-indicator" style="background-color: {{ $item['color'] }};"></div>
                <div class="legend-content">
                    <div class="legend-label">{{ $item['name'] }}</div>
                    <div class="legend-value">{{ $item['val'] }}%</div>
                </div>
                <div class="legend-buttons">
                    @if($allowInfo ?? true)
                    <span class="btn-legend btn-info" data-poolid="{{ $poolId ?? '' }}"
                        data-analysisid="{{ $item['analysis_id'] }}" data-container="body" data-toggle="popover"
                        data-placement="top" data-html="true" data-content="">
                        <i class="fas fa-info-circle"></i>
                    </span>
                    @endif
                    <div class="add-to-cart-container">
                        <livewire:dashboard.single-analyses-add-to-cart
                            :wire:key="'chart-add-to-cart-line-' . $item['analysis_id']" :poolId="$poolId"
                            :analysesId="$item['analysis_id']" />
                    </div>
                </div>
            </div>
            @endforeach
        </div>
    </div>

    {{-- Popover içerikleri için gizli div'ler --}}
    @if($allowInfo ?? true)
    <div class="d-none">
        @foreach($fullResults as $item)
        <div id="popover-content-{{ $poolId ?? '' }}-{{ $item['analysis_id'] }}" class="hide">
            @if(!empty($item['description']))
            <h6>{{__('action.main_desc')}}</h6>
            <p>{!! $item['description'] !!}</p>
            @endif
            @if(isset($item['desc_img']))
            <img loading="lazy" src="{{ asset('/storage/analyse/images/description') }}/{{ $item['desc_img'] }}"
                class="popover-showimg" alt="" height="250px" width="auto">
            @endif
            <hr>
            @if(!empty($item['bodyDesc']))
            <h6>{{__('action.body')}}</h6>
            <p>{!! $item['bodyDesc'] !!}</p>
            @endif
            <hr>
            @if(!empty($item['mentalDesc']))
            <h6>{{__('action.mental')}}</h6>
            <p>{!! $item['mentalDesc'] !!}</p>
            @endif
        </div>
        @endforeach
    </div>
    @endif
</div>

@assets
<script src="{{ asset('/js/chart/chart.min.js') }}"></script>
<script src="{{ asset('/js/chart/chartjs-plugin-annotation.min.js') }}"></script>
<style>
    .line-chart-container {
        width: 100%;
    }

    .regular-line-container {
        display: flex;
        flex-direction: column;
        gap: 20px;
    }

    .line-chart-legend {
        display: flex;
        flex-wrap: wrap;
        gap: 15px;
        justify-content: center;
        padding: 15px;
        background: #f8f9fa;
        border-radius: 8px;
    }

    .horizontal-line-container {
        display: flex;
        width: 100%;
        align-items: stretch;
    }

    .horizontal-legend {
        flex: 0 0 auto;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        padding-bottom: 30px;
    }

    .horizontal-legend-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        white-space: nowrap;
        font-size: 11px;
    }

    .horizontal-legend-item .legend-buttons {
        display: flex;
        align-items: center;
    }

    .horizontal-legend-item .legend-label {
        font-weight: 600;
        color: #2c3e50;
        margin-right: 6px;
        max-width: 120px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .horizontal-legend-item .add-to-cart-container {
        line-height: 1;
        margin-left: 4px;
    }

    .horizontal-legend-item .add-to-cart-container .cart-wrapper {
        transform: none !important;
        margin-left: 0 !important;
    }

    .horizontal-chart-wrapper {
        flex: 1 1 auto;
        min-width: 0;
    }

    .line-legend-item {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 8px 12px;
        background: white;
        border-radius: 6px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        font-size: 12px;
    }

    .legend-color-indicator {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        flex-shrink: 0;
    }

    .legend-content {
        display: flex;
        flex-direction: column;
        gap: 2px;
    }

    .legend-label {
        font-weight: 600;
        color: #2c3e50;
        font-size: 11px;
    }

    .legend-value {
        font-weight: bold;
        font-size: 12px;
        color: #666;
    }

    .legend-buttons {
        display: flex;
        align-items: center;
        gap: 4px;
    }

    .line-chart-container .btn-legend {
        border: none;
        cursor: pointer;
        font-weight: 500;
        display: flex;
        align-items: center;
        justify-content: center;
        background: inherit;
        color: inherit;
    }

    .line-chart-container .btn-legend:hover {
        background-color: inherit;
        background: inherit;
        color: inherit;
    }

    .line-chart-container .btn-info .fas.fa-info-circle {
        font-size: 14px;
    }

    .line-chart-container .add-to-cart-container {
        line-height: 1;
    }

    .line-chart-container .add-to-cart-container .cart-wrapper {
        transform: none !important;
        margin-left: 0 !important;
    }

    .line-chart-wrapper {
        width: 100%;
        position: relative;
    }

    @media (max-width: 768px) {
        .line-chart-legend {
            flex-direction: column;
            align-items: stretch;
        }

        .line-legend-item {
            justify-content: space-between;
        }
    }

    @media (max-width: 450px) {
        .line-chart-container .horizontal-line-container .horizontal-chart-wrapper .horizontalBar {
            min-height: {{ count($fullResults) > 7 ? '180px' : '160px' }};
            height: {{ count($fullResults) > 7 ? '180px' : '160px' }};
        }
    }

    @media (min-width: 992px) and (max-width: 1440px) {
        .line-chart-container .horizontal-line-container .horizontal-chart-wrapper .horizontalBar {
            min-height: {{ count($fullResults) > 7 ? '180px' : '160px' }};
            height: {{ count($fullResults) > 7 ? '180px' : '160px' }};
        }
    }

    @media (min-width: 1440px) and (max-width: 1500px) {
        .horizontal-line-container .horizontal-legend {
            padding-bottom: 35px;
        }
    }
</style>
@endassets

@script
<script>
    let id = @js($uniqueId);
    let filter = @js($filter);
    let results = @json($results);
    let labels = @json($labels);
    let fullResults = @json($fullResults);
    let barThickness = @js($barThickness);
    let currentChartType;

    function initializeLegendTooltips() {
        $('.legend-label[title]').each(function() {
            const element = $(this);
            const textWidth = element[0].scrollWidth;
            const containerWidth = element.width();

            if (textWidth > containerWidth) {
                element.tooltip({
                    placement: 'top',
                    trigger: 'hover',
                    delay: { show: 300, hide: 100 }
                });
            }
        });
    }

    function getChartType() {
        const width = window.innerWidth;

        if (width <= 450) {
            return 'horizontalBar';
        }

        if (width < 1440 && width >= 992) {
            return 'horizontalBar';
        }

        if (width <= 1660 && width >= 992) {
            if ($('html').hasClass('layout-collapsed')) {
                return 'line';
            } else {
                return 'horizontalBar';
            }
        }

        return 'line';
    }

    function updateChartVisibility(chartType) {
        const regularLineContainer = document.getElementById(`${id}_regular_line_container`);
        const horizontalLineContainer = document.getElementById(`${id}_horizontal_line_container`);

        if (!regularLineContainer || !horizontalLineContainer) return;

        if (chartType === 'line') {
            regularLineContainer.style.display = 'block';
            horizontalLineContainer.style.display = 'none';
        } else {
            regularLineContainer.style.display = 'none';
            horizontalLineContainer.style.display = 'flex';
        }
    }

    function debounce(func, wait) {
        let timeout;
        return function (...args) {
            const context = this;
            clearTimeout(timeout);
            timeout = setTimeout(() => func.apply(context, args), wait);
        };
    }

    function createLineChart(id, chartType, labels, datasets, filters) {
        const commonOptions = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: { display: false },
                zoom: {
                    pan: { enabled: true, mode: 'xy' },
                    zoom: { enabled: true, mode: 'xy' },
                },
            },
        };

        const chartOptions = {
            line: {
                type: 'line',
                options: {
                    ...commonOptions,
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                stepSize: 10,
                                callback: function(value) {
                                    return value + '%';
                                }
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    },
                    elements: {
                        line: {
                            tension: 0.4 // Smooths the line slightly
                        }
                    }
                },
            },
            horizontalBar: {
                type: 'bar',
                options: {
                    ...commonOptions,
                    indexAxis: 'y',
                    scales: {
                        y: {
                            display: false
                        },
                        x: {
                            ticks: {
                                callback: function(value) {
                                    return value + '%';
                                }
                            }
                        }
                    }
                },
            },
        };

        const config = {
            type: chartOptions[chartType].type,
            data: {
                labels: labels,
                datasets: getFormattedDatasets(datasets, chartType, filters),
            },
            options: chartOptions[chartType].options,
        };

        const canvasId = (chartType === 'line')
            ? `${id}_module_head_chart`
            : `${id}_module_head_chart_horizontal`;

        const canvas = document.getElementById(canvasId);
        if (!canvas) return;
        const ctx = canvas.getContext('2d');

        if (window.chartInstances && window.chartInstances[canvasId]) {
            window.chartInstances[canvasId].destroy();
        }
        window.chartInstances = { ...(window.chartInstances || {}), [canvasId]: new Chart(ctx, config) };
    }

    function getFormattedDatasets(datasets, chartType, filters) {
        if (filters) {
            return datasets.map((data, index) => ({
                data: data,
                backgroundColor: index === 0 ? 'rgba(255, 99, 132, 0.1)' : 'rgba(75, 192, 192, 0.1)',
                borderColor: index === 0 ? 'rgba(255, 99, 132, 1)' : 'rgba(47, 171, 102, 1)',
                borderWidth: 2,
                pointBackgroundColor: index === 0 ? 'rgba(255, 99, 132, 1)' : 'rgba(47, 171, 102, 1)',
                pointBorderColor: '#fff',
                pointRadius: 4,
                pointHoverRadius: 6,
                fill: chartType === 'line' ? true : false,
                tension: chartType === 'line' ? 0.4 : 0
            }));
        }

        const chartColors = getChartColors(datasets);
        const commonDataset = {
            data: datasets,
            backgroundColor: chartType === 'line' ? 'rgba(47, 171, 102, 0.1)' : chartColors.backgroundColor,
            borderColor: chartType === 'line' ? 'rgba(47, 171, 102, 1)' : chartColors.borderColor,
            borderWidth: chartType === 'line' ? 2 : 1
        };

        if (chartType === 'horizontalBar') {
            return [{ ...commonDataset, barThickness: barThickness }];
        }

        if (chartType === 'line') {
            return [{
                ...commonDataset,
                pointBackgroundColor: chartColors.backgroundColor,
                pointBorderColor: chartColors.borderColor,
                pointRadius: 4,
                pointHoverRadius: 6,
                fill: true,
                tension: 0.4
            }];
        }

        return [commonDataset];
    }

    function getChartColors(dataSet) {
        // Add null safety check
        if (!dataSet || !Array.isArray(dataSet)) {
            return { backgroundColor: [], borderColor: [] };
        }

        const getColor = value => {
            if (value <= 10) return ['rgba(232, 78, 27, 1)', 'rgb(232, 78, 27)'];
            if (value <= 69) return ['rgba(248, 177, 51, 1)', 'rgb(248, 177, 51)'];
            if (value <= 100) return ['rgba(47, 171, 102, 1)', 'rgb(47, 171, 102)'];
            return ['rgba(0, 0, 0, 0.2)', 'rgb(0, 0, 0)'];
        };

        return dataSet.reduce((colors, value) => {
            const [bgColor, borderColor] = getColor(value);
            colors.backgroundColor.push(bgColor);
            colors.borderColor.push(borderColor);
            return colors;
        }, { backgroundColor: [], borderColor: [] });
    }

    currentChartType = getChartType();
    updateChartVisibility(currentChartType);
    createLineChart(id, currentChartType, labels, results, filter);

    const optimizedResize = debounce(() => {
        const newChartType = getChartType();
        if (newChartType !== currentChartType) {
            currentChartType = newChartType;
            updateChartVisibility(currentChartType);
            createLineChart(id, currentChartType, labels, results, filter);

            setTimeout(() => {
                $('.legend-label[title]').tooltip('dispose');
                initializeLegendTooltips();
            }, 100);
        }
    }, 200);

    window.addEventListener('resize', optimizedResize);

    $(function ($) {
        $('[data-toggle="popover"]').each(function () {
            var popoverTrigger = $(this);
            var poolId = popoverTrigger.data('poolid');
            var analysisId = popoverTrigger.data('analysisid');
            var popoverContent = $('#popover-content-' + poolId + '-' + analysisId);
            popoverTrigger.popover({
                html: true,
                content: function () {
                    return popoverContent.html();
                }
            });
        });

        initializeLegendTooltips();
    });

    $('body').on('click', function (e) {
        $('[data-toggle=popover]').each(function () {
            if (!$(this).is(e.target) && $(this).has(e.target).length === 0 && $('.popover').has(e.target).length === 0) {
                $(this).popover('hide');
            }
        });
    });
</script>
@endscript